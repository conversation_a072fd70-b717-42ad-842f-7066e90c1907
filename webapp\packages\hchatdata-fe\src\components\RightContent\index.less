

@pro-header-hover-bg: rgba(0, 0, 0, 0.025);

.menu {
  :global(.anticon) {
    margin-right: 8px;
  }

  :global(.ant-dropdown-menu-item) {
    min-width: 160px;
  }
}

.right {
  display: flex;
  float: right;
  height: 48px;
  margin-left: auto;
  overflow: hidden;

  .action {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0 12px;
    cursor: pointer;
    transition: all 0.3s;
    color: #fff;
    margin-right: 10px;
    >span {
      vertical-align: middle;
    }


    &:hover {
      background: rgba(232, 232, 232, 0.65);
      border-radius: 8px;
    }


    // &:global(.opened) {
    //   background: @pro-header-hover-bg;
    // }
  }

  .search {
    padding: 0 12px;

    &:hover {
      background: transparent;
    }
  }

  .account {
    // 确保 account 元素的背景色优先级
    &.action {
      background: #fff !important;
    }

    .avatar {
      margin-right: 8px;
      color: var(--tme-primary-color);
      vertical-align: top;
      background: rgba(150, 150, 150, 0.85);
      border-radius: 50% !important;
      overflow: hidden;

      // 确保内部图片也是圆形
      :global(.ant-avatar-image) {
        border-radius: 50%;
      }

      // 确保icon图片也是圆形
      img {
        border-radius: 50%;
      }
    }
    .userName {
      color: #000;
      font-family: var(--tencent-font-family);
    }
  }
}
